import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellRenderer;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class RegentsUhUhUhTheTheUhhhhhh extends JFrame {

    private JTable table;
    private JPanel mainPanel;
    private JTabbedPane tabbedPane;
    private String currentSubject = "Living Environment";
    private List<QuizQuestion> currentQuizQuestions = new ArrayList<>();
    private int currentQuestionIndex = 0;
    private int score = 0;

    public RegentsUhUhUhTheTheUhhhhhh() {
        setTitle("Simple Study Guide");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 800);
        setLocationRelativeTo(null);

        mainPanel = new JPanel(new BorderLayout());
        tabbedPane = new JTabbedPane();

        // Create subject selection
        JPanel topPanel = new JPanel();
        JComboBox<String> subjectSelector = new JComboBox<>(new String[]{
                "Living Environment",
                "Global History",
                "Geometry",
                "Java Programming",
        });
        subjectSelector.addActionListener(e -> {
            String selectedSubject = (String) subjectSelector.getSelectedItem();
            updateContent(selectedSubject);
            updateQuizTab(selectedSubject);
        });
        topPanel.add(new JLabel("Pick a subject: "));
        topPanel.add(subjectSelector);

        // Create study guide tab (this is already making me regret)
        JPanel studyGuidePanel = new JPanel(new BorderLayout());
        table = new JTable();
        table.setRowHeight(100);
        table.getTableHeader().setReorderingAllowed(false);
        table.setDefaultEditor(Object.class, null);
        table.setFont(new Font("Arial", Font.PLAIN, 14));
        table.getTableHeader().setFont(new Font("Arial", Font.BOLD, 14));
        JScrollPane scrollPane = new JScrollPane(table);
        studyGuidePanel.add(scrollPane, BorderLayout.CENTER);

        // Create quiz tab
        JPanel quizPanel = createQuizPanel();

        tabbedPane.addTab("Study Guide", studyGuidePanel);
        tabbedPane.addTab("Quiz", quizPanel);

        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(tabbedPane, BorderLayout.CENTER);

        add(mainPanel);
        updateContent("Living Environment");
        updateQuizTab("Living Environment");
    }

    private JPanel createQuizPanel() {
        JPanel quizPanel = new JPanel(new BorderLayout());

        // Question panel (here's a questionhow many redbulls am i on when making this? the answer is 5!)
        JPanel questionPanel = new JPanel(new BorderLayout());
        JLabel questionLabel = new JLabel("", SwingConstants.CENTER);
        questionLabel.setFont(new Font("Arial", Font.BOLD, 16));
        questionPanel.add(questionLabel, BorderLayout.CENTER);

        // Options panel (i dont have an option in making this)
        JPanel optionsPanel = new JPanel(new GridLayout(0, 1));
        ButtonGroup optionsGroup = new ButtonGroup();
        JRadioButton[] optionButtons = new JRadioButton[4];
        for (int i = 0; i < 4; i++) {
            optionButtons[i] = new JRadioButton();
            optionButtons[i].setFont(new Font("Arial", Font.PLAIN, 14));
            optionsGroup.add(optionButtons[i]);
            optionsPanel.add(optionButtons[i]);
        }

        // Bottom panel (bottom?)
        JPanel bottomPanel = new JPanel(new BorderLayout());
        JButton submitButton = new JButton("Submit Answer");
        JButton nextButton = new JButton("Next Question");
        nextButton.setEnabled(false);
        JLabel scoreLabel = new JLabel("Score: 0/0", SwingConstants.CENTER);
        scoreLabel.setFont(new Font("Arial", Font.BOLD, 14));

        JPanel buttonPanel = new JPanel();
        buttonPanel.add(submitButton);
        buttonPanel.add(nextButton);
        bottomPanel.add(buttonPanel, BorderLayout.CENTER);
        bottomPanel.add(scoreLabel, BorderLayout.SOUTH);

        // Feedback label (i need feedback for my code, please help me)
        JLabel feedbackLabel = new JLabel("", SwingConstants.CENTER);
        feedbackLabel.setFont(new Font("Arial", Font.ITALIC, 14));
        feedbackLabel.setForeground(Color.BLUE);

        // Add components to quiz panel
        quizPanel.add(questionPanel, BorderLayout.NORTH);
        quizPanel.add(new JScrollPane(optionsPanel), BorderLayout.CENTER);

        // Create a combined south panel for feedback and buttons
        JPanel southPanel = new JPanel(new BorderLayout());
        southPanel.add(feedbackLabel, BorderLayout.CENTER);
        southPanel.add(bottomPanel, BorderLayout.SOUTH);
        quizPanel.add(southPanel, BorderLayout.SOUTH);

        // Action listeners (someone listen to me)
        submitButton.addActionListener(e -> {
            int selectedOption = -1;
            for (int i = 0; i < 4; i++) {
                if (optionButtons[i].isSelected()) {
                    selectedOption = i;
                    break;
                }
            }

            if (selectedOption == -1) {
                feedbackLabel.setText("Please select an answer!");
                feedbackLabel.setForeground(Color.RED);
                return;
            }

            QuizQuestion currentQuestion = currentQuizQuestions.get(currentQuestionIndex);
            if (selectedOption == currentQuestion.correctAnswer) {
                score++;
                feedbackLabel.setText("Correct! " + currentQuestion.explanation);
                feedbackLabel.setForeground(Color.GREEN);
            } else {
                feedbackLabel.setText("Incorrect. The correct answer is: " +
                        currentQuestion.options[currentQuestion.correctAnswer] + ". " + currentQuestion.explanation);
                feedbackLabel.setForeground(Color.RED);
            }

            scoreLabel.setText("Score: " + score + "/" + (currentQuestionIndex + 1));
            submitButton.setEnabled(false);
            nextButton.setEnabled(true);
        });

        nextButton.addActionListener(e -> {
            currentQuestionIndex++;
            if (currentQuestionIndex < currentQuizQuestions.size()) {
                displayQuestion(currentQuestionIndex, questionLabel, optionButtons, feedbackLabel);
                submitButton.setEnabled(true);
                nextButton.setEnabled(false);
                optionsGroup.clearSelection();
            } else {
                questionLabel.setText("Quiz completed! Final score: " + score + "/" + currentQuizQuestions.size());
                for (JRadioButton button : optionButtons) {
                    button.setVisible(false);
                }
                submitButton.setEnabled(false);
                nextButton.setEnabled(false);
            }
        });

        return quizPanel;
    }
        /*alr so 10 questions for each subject  cuz  10 because three questions is too little yk

         i know its like really tedious but please do it */

    // done
    private void updateContent(String subject) {
        this.currentSubject = subject;
        DefaultTableModel model = new DefaultTableModel(new Object[]{"Units", "Key Topics"}, 0);

        switch (subject) {
            case "Living Environment":
                model.addRow(new Object[]{"Scientific Inquiry",
                        "• Scientific Method: observation, hypothesis, experiment, analysis, conclusion\n" +
                        "• Experimental Design: variables (independent, dependent, controlled), control groups\n" +
                        "• Data Analysis: graphs, statistics, error analysis\n" +
                        "• Lab Safety: protective equipment, proper handling of materials"});

                model.addRow(new Object[]{"Biochemistry",
                        "• Organic Compounds: carbohydrates (energy), proteins (structure), lipids (energy storage), nucleic acids (genetic info)\n" +
                        "• Enzymes: biological catalysts, lock-and-key model, factors affecting activity (pH, temperature)\n" +
                        "• Homeostasis: maintaining internal stability despite external changes\n" +
                        "• pH Scale: acid (0-7), neutral (7), base/alkaline (7-14)"});

                model.addRow(new Object[]{"Cells",
                        "• Cell Theory: all living things made of cells, cells come from existing cells\n" +
                        "• Organelles: nucleus (genetic material), mitochondria (cellular respiration), ribosomes (protein synthesis)\n" +
                        "• Cell Membrane: selectively permeable barrier, controls what enters/exits the cell\n" +
                        "• Transport: diffusion, osmosis, active transport, endocytosis, exocytosis"});

                model.addRow(new Object[]{"Human Body Systems",
                        "• Nervous System: brain, spinal cord, neurons, sensory/motor functions\n" +
                        "• Circulatory System: heart, blood vessels, blood cells, oxygen/nutrient transport\n" +
                        "• Respiratory System: lungs, gas exchange, breathing mechanics\n" +
                        "• Digestive System: breakdown of food, nutrient absorption"});

                model.addRow(new Object[]{"Ecology",
                        "• Ecosystem Components: producers (plants), consumers (animals), decomposers (fungi, bacteria)\n" +
                        "• Energy Flow: food chains, food webs, energy pyramids\n" +
                        "• Biogeochemical Cycles: carbon, nitrogen, water cycles\n" +
                        "• Photosynthesis: plants convert sunlight, water, and CO2 into glucose and oxygen"});

                model.addRow(new Object[]{"Genetics",
                        "• DNA: structure, function, genetic information storage\n" +
                        "• Protein Synthesis: transcription, translation\n" +
                        "• Inheritance: Mendel's laws, dominant/recessive traits\n" +
                        "• Genetic Disorders: chromosomal abnormalities, gene mutations"});
                break;

            case "Global History":
                model.addRow(new Object[]{"Ancient World",
                        "• River Valley Civilizations: Mesopotamia (Tigris/Euphrates), Egypt (Nile), Indus Valley, China (Yellow River)\n" +
                        "• Classical Empires: Greek, Roman, Persian, Han China\n" +
                        "• Major Religions: Judaism, Christianity, Islam, Hinduism, Buddhism\n" +
                        "• Trade Routes: Silk Road, Mediterranean sea routes, Indian Ocean trade"});

                model.addRow(new Object[]{"Middle Ages",
                        "• Feudal Systems: lords, vassals, peasants, manorialism\n" +
                        "• Byzantine Empire: Eastern Roman Empire, Constantinople, Orthodox Christianity\n" +
                        "• Islamic Golden Age: scientific advances, preservation of classical knowledge\n" +
                        "• Crusades: religious wars between Christians and Muslims over Holy Land"});

                model.addRow(new Object[]{"The American Civil War",
                        "• Causes:\n" +
                        "  - Slavery and states' rights\n" +
                        "  - Economic differences between North and South\n" +
                        "  - Missouri Compromise of 1820\n" +
                        "  - Kansas-Nebraska Act of 1854\n" +
                        "  - Election of Abraham Lincoln (1860)\n\n" +
                        "• Reconstruction Period:\n" +
                        "  - Presidential vs. Radical Reconstruction\n" +
                        "  - 13th, 14th, and 15th Amendments\n" +
                        "  - Freedmen's Bureau\n" +
                        "  - Black Codes and Jim Crow Laws\n" +
                        "  - End of Reconstruction (1877)"});

                model.addRow(new Object[]{"Modern Era",
                        "• Industrial Revolution: mechanization, factories, urbanization, middle class growth\n" +
                        "• Nationalism: unification of Italy and Germany, national identity movements\n" +
                        "• Imperialism: European colonization of Africa and Asia\n" +
                        "• World Wars: WWI (1914-1918), WWII (1939-1945), Allied vs Axis powers\n" +
                        "• Cold War: US vs Soviet Union (1947-1991), arms race, proxy wars"});

                model.addRow(new Object[]{"Renaissance & Reformation",
                        "• Renaissance: rebirth of classical learning, began in Italy 14th century\n" +
                        "• Humanism: focus on human potential and achievements\n" +
                        "• Art & Science: da Vinci, Michelangelo, Copernicus, Galileo\n" +
                        "• Protestant Reformation: Martin Luther, 95 Theses, Catholic Church reforms"});

                model.addRow(new Object[]{"Democracy & Rights",
                        "• Magna Carta (1215): limited monarchy power in England\n" +
                        "• Democracy: originated in Athens, Greece (508 BCE)\n" +
                        "• Enlightenment: reason, natural rights, social contract\n" +
                        "• Revolutions: American, French, Latin American independence movements"});
                break;

            case "Geometry":
                model.addRow(new Object[]{"Basics",
                        "• Points and Lines: undefined terms, line segments, rays\n" +
                        "• Angles: acute (<90°), right (90°), obtuse (>90°), straight (180°)\n" +
                        "• Coordinate Geometry: Cartesian plane, plotting points, distance formula √((x₂-x₁)² + (y₂-y₁)²)\n" +
                        "• Transformations: translations, rotations, reflections, dilations"});

                model.addRow(new Object[]{"Triangles",
                        "• Congruence: SSS, SAS, ASA, AAS postulates\n" +
                        "• Similarity: proportional sides, equal angles\n" +
                        "• Special Right Triangles: 30-60-90, 45-45-90\n" +
                        "• Pythagorean Theorem: a² + b² = c² (in right triangles)\n" +
                        "• Interior Angle Sum: always equals 180 degrees"});

                model.addRow(new Object[]{"Circles",
                        "• Circle Equations: (x-h)² + (y-k)² = r² (center at (h,k))\n" +
                        "• Arc Measures: central angles, inscribed angles\n" +
                        "• Inscribed Angles: measure half the intercepted arc\n" +
                        "• Tangent Lines: perpendicular to radius at point of tangency\n" +
                        "• Circumference: 2πr or πd\n" +
                        "• Area: πr²"});

                model.addRow(new Object[]{"Polygons",
                        "• Quadrilaterals: 4-sided polygons, interior angles sum to 360°\n" +
                        "• Regular Polygons: all sides and angles equal\n" +
                        "• Area Formulas: trapezoid ½(b₁+b₂)h, rectangle (lw), square (s²)\n" +
                        "• Interior Angle Sum: (n-2)×180° for n-sided polygon"});

                model.addRow(new Object[]{"3D Geometry",
                        "• Solids: prisms, pyramids, cylinders, cones, spheres\n" +
                        "• Volume: prism (Bh), sphere (4/3πr³), cylinder (πr²h)\n" +
                        "• Surface Area: sum of all faces\n" +
                        "• Cross-sections: 2D shapes formed by slicing 3D objects"});

                model.addRow(new Object[]{"Trigonometry",
                        "• Basic Ratios: sine, cosine, tangent\n" +
                        "• Right Triangle Trig: SOH-CAH-TOA\n" +
                        "• Law of Sines, Law of Cosines\n" +
                        "• Applications: finding missing sides and angles"});
                break;

            case "Java Programming":
                model.addRow(new Object[]{"Basics",
                        "• Data Types: primitive (int, double, boolean, char) vs reference (String, arrays, objects)\n" +
                        "• Variables: declaration, initialization, naming conventions\n" +
                        "• Operators: arithmetic (+, -, *, /, %), comparison (==, !=, >, <), logical (&&, ||, !)\n" +
                        "• Arrays: fixed-size collections, zero-indexed, declaration with new keyword"});

                model.addRow(new Object[]{"Control Structures",
                        "• Conditionals: if-else, switch statements\n" +
                        "• Loops: for, while, do-while, enhanced for-each\n" +
                        "• Switch Statements: multiple case selection\n" +
                        "• Exception Handling: try-catch blocks, throws declaration"});

                model.addRow(new Object[]{"OOP",
                        "• Classes and Objects: blueprints vs instances\n" +
                        "• Inheritance: extends keyword, superclass/subclass relationships\n" +
                        "• Polymorphism: method overriding, method overloading\n" +
                        "• Encapsulation: private fields, public getters/setters\n" +
                        "• Interfaces: collection of abstract methods, implemented with implements keyword"});

                model.addRow(new Object[]{"Java Specifics",
                        "• Main Method: public static void main(String[] args)\n" +
                        "• Object Creation: using new keyword\n" +
                        "• Final Keyword: constants, preventing inheritance/overriding\n" +
                        "• String vs == Comparison: .equals() for content, == for reference equality"});

                model.addRow(new Object[]{"Collections",
                        "• ArrayList: dynamic-size ordered collection\n" +
                        "• HashSet: unordered collection with no duplicates\n" +
                        "• TreeSet: sorted collection with no duplicates\n" +
                        "• HashMap: key-value pairs, fast lookups"});

                model.addRow(new Object[]{"Advanced Concepts",
                        "• Abstract Classes: partial implementation, cannot be instantiated\n" +
                        "• Interfaces: pure abstraction, multiple inheritance\n" +
                        "• Generics: type-safe collections, parameterized types\n" +
                        "• Multithreading: concurrent execution"});
                break;
        }

        table.setModel(model);

        // Fix the display issues by setting appropriate column widths and row heights
        table.getColumnModel().getColumn(0).setPreferredWidth(150);
        table.getColumnModel().getColumn(1).setPreferredWidth(650);  // Increased width

        // Set row height based on content
        for (int row = 0; row < table.getRowCount(); row++) {
            String content = (String) model.getValueAt(row, 1);
            int lineCount = content.split("\n").length;
            int rowHeight = Math.max(lineCount * 20, 100);  // Adjust height based on content
            table.setRowHeight(row, rowHeight);
        }

        // Enable text wrapping in the table cells
        table.getColumnModel().getColumn(1).setCellRenderer(new MultiLineTableCellRenderer());
    }

    private void updateQuizTab(String subject) {
        // Clear existing questions and reset counters
        currentQuizQuestions.clear();
        currentQuestionIndex = 0;
        score = 0;

        switch (subject) {
            case "Living Environment":
                // Basic questions
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the first step in the scientific method?",
                        new String[]{"Form a hypothesis", "Make an observation", "Conduct an experiment", "Analyze data"},
                        1,
                        "The scientific method begins with making an observation about the natural world."
                ));
                //these are genuinely the most basic question i could think of
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of these is NOT an organic compound?",
                        new String[]{"Carbohydrate", "Protein", "Lipid", "Water"},
                        3,
                        "Water is an inorganic compound essential for life but doesn't contain carbon."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the function of the mitochondria?",
                        new String[]{"Protein synthesis", "Cellular respiration", "Photosynthesis", "Waste removal"},
                        1,
                        "Mitochondria are known as the powerhouse of the cell, producing ATP through cellular respiration."
                ));

                currentQuizQuestions.add(new QuizQuestion(
                        "Which organelle is responsible for protein synthesis?",
                        new String[]{"Mitochondria", "Ribosomes", "Golgi apparatus", "Lysosomes"},
                        1,
                        "Ribosomes are the cellular structures where proteins are assembled following mRNA instructions."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is homeostasis?",
                        new String[]{"Cell division", "Maintenance of internal stability", "Genetic mutation", "Energy production"},
                        1,
                        "Homeostasis is the process by which organisms maintain stable internal conditions despite changes in the environment."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of the following is NOT a function of the cell membrane?",
                        new String[]{"Transport of materials", "Cell recognition", "Energy production", "Protection"},
                        2,
                        "Energy production primarily occurs in the mitochondria, not the cell membrane."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the primary role of DNA?",
                        new String[]{"Energy storage", "Protein synthesis", "Genetic information storage", "Cell division"},
                        2,
                        "DNA stores genetic information that serves as instructions for protein synthesis and inheritance."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of these is an example of a negative feedback mechanism?",
                        new String[]{"Blood clotting", "Temperature regulation", "Childbirth contractions", "Fruit ripening"},
                        1,
                        "Temperature regulation is a classic example of negative feedback, where the body works to return to normal when temperature deviates."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the process by which plants make their own food?",
                        new String[]{"Respiration", "Fermentation", "Photosynthesis", "Digestion"},
                        2,
                        "Photosynthesis is the process where plants use sunlight, water, and carbon dioxide to produce glucose and oxygen."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of the following is NOT a component of an ecosystem?",
                        new String[]{"Producers", "Consumers", "Decomposers", "Electrons"},
                        3,
                        "Electrons are subatomic particles, not components of an ecosystem. Ecosystems consist of living organisms and their environment."
                ));
                break;

            case "Global History":
                // ok james u can do this one because you know history right?

                // yeah totally

                //HOW DID YOU DO THIS SO FAST?
                currentQuizQuestions.add(new QuizQuestion(
                        "Which civilization developed along the Nile River?",
                        new String[]{"Mesopotamia", "Ancient Egypt", "Indus Valley", "Ancient China"},
                        1,
                        "Ancient Egypt developed along the Nile River, which provided fertile land for agriculture."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What was the main cause of the American Civil War?",
                        new String[]{"Taxation without representation", "Slavery", "Territorial disputes", "Religious differences"},
                        1,
                        "While multiple factors contributed, slavery was the central issue dividing the North and South."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which event marked the beginning of World War I?",
                        new String[]{"Invasion of Poland", "Assassination of Archduke Franz Ferdinand", "Bombing of Pearl Harbor", "Signing of the Treaty of Versailles"},
                        1,
                        "The assassination of Archduke Franz Ferdinand of Austria-Hungary in 1914 triggered a series of events leading to WWI."
                ));

                currentQuizQuestions.add(new QuizQuestion(
                        "Which empire was known for building extensive road systems throughout Europe and North Africa?",
                        new String[]{"Greek Empire", "Persian Empire", "Roman Empire", "Ottoman Empire"},
                        2,
                        "The Roman Empire built over 250,000 miles of roads to facilitate military movement and trade."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "The Renaissance period primarily began in which region?",
                        new String[]{"England", "France", "Italy", "Germany"},
                        2,
                        "The Renaissance began in Italy during the 14th century before spreading throughout Europe."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of the following was NOT one of the Allied Powers during World War II?",
                        new String[]{"United States", "Soviet Union", "Great Britain", "Japan"},
                        3,
                        "Japan was part of the Axis Powers along with Nazi Germany and Fascist Italy."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "The Magna Carta limited the power of which institution?",
                        new String[]{"The Church", "The Monarchy", "The Parliament", "The Military"},
                        1,
                        "The Magna Carta, signed in 1215, limited the power of the English monarchy and established that the king was subject to the law."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of these was a major effect of the Industrial Revolution?",
                        new String[]{"Decreased urbanization", "Growth of the middle class", "Reduction in pollution", "Decline in international trade"},
                        1,
                        "The Industrial Revolution led to the growth of the middle class as new economic opportunities emerged."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "The Cold War was primarily a conflict between which two powers?",
                        new String[]{"Germany and France", "United States and Soviet Union", "China and Japan", "Britain and Russia"},
                        1,
                        "The Cold War (1947-1991) was primarily a geopolitical tension between the United States and the Soviet Union."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which ancient civilization developed the concept of democracy?",
                        new String[]{"Rome", "Egypt", "Athens", "Persia"},
                        2,
                        "Athens, Greece developed the first known democracy around 508 BCE, though it was limited to male citizens."
                ));
                break;

            case "Geometry":
                // journey do this one too cuz ur asian

                //i cant deny that :sob:
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the sum of interior angles in a triangle?",
                        new String[]{"90 degrees", "180 degrees", "270 degrees", "360 degrees"},
                        1,
                        "The sum of interior angles in any triangle is always 180 degrees."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "In a right triangle, what is the side opposite the right angle called?",
                        new String[]{"Adjacent side", "Opposite side", "Hypotenuse", "Leg"},
                        2,
                        "The hypotenuse is always the longest side in a right triangle, opposite the right angle."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the formula for the circumference of a circle?",
                        new String[]{"πr²", "2πr", "πd", "Both 2 and 3"},
                        3,
                        "Circumference can be calculated as 2πr (2 × π × radius) or πd (π × diameter)."
                ));

                currentQuizQuestions.add(new QuizQuestion(
                        "What is the Pythagorean theorem?",
                        new String[]{"a² + b² = c²", "a + b = c", "a × b = c", "a/b = c"},
                        0,
                        "In a right triangle, the square of the hypotenuse equals the sum of squares of the other two sides."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the formula for the area of a circle?",
                        new String[]{"2πr", "πr", "πr²", "2πr²"},
                        2,
                        "The area of a circle is calculated as π multiplied by the radius squared (πr²)."
                ));
                // THIS IS SO REPETITIVE OH MY GOD
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the sum of the interior angles of a quadrilateral?",
                        new String[]{"180 degrees", "270 degrees", "360 degrees", "540 degrees"},
                        2,
                        "The sum of interior angles in any quadrilateral is always 360 degrees."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Two triangles are similar if:",
                        new String[]{"They have the same area", "Their corresponding angles are equal", "They have the same perimeter", "They have at least one side of equal length"},
                        1,
                        "Similar triangles have corresponding angles that are equal and corresponding sides that are proportional."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the formula for the area of a trapezoid?",
                        new String[]{"bh", "½bh", "½(a+c)h", "½(b₁+b₂)h"},
                        3,
                        "The area of a trapezoid is calculated as ½ times the sum of the parallel sides times the height."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the value of π (pi) rounded to two decimal places?",
                        new String[]{"3.14", "3.41", "3.16", "3.12"},
                        0,
                        "π is approximately equal to 3.14159..., which rounds to 3.14 when expressed with two decimal places."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "In coordinate geometry, what is the distance formula between points (x₁,y₁) and (x₂,y₂)?",
                        new String[]{"√((x₂-x₁)² + (y₂-y₁)²)", "(x₂-x₁) + (y₂-y₁)", "½((x₂-x₁) + (y₂-y₁))", "(x₂-x₁)(y₂-y₁)"},
                        0,
                        "The distance formula is derived from the Pythagorean theorem and calculates the straight-line distance between two points."
                ));
                break;

            case "Java Programming":
                // bruther how are you doing this so fast

                //why are you so slow
                currentQuizQuestions.add(new QuizQuestion(
                        "Which keyword is used to create an object in Java?",
                        new String[]{"create", "new", "object", "make"},
                        1,
                        "The 'new' keyword is used to instantiate (create) a new object in Java."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the correct way to declare a main method in Java?",
                        new String[]{
                                "public static int main(String[] args)",
                                "public void main(String[] args)",
                                "public static void main(String[] args)",
                                "static void main(String[] args)"
                        },
                        2,
                        "The main method must be public, static, void, and take a String array parameter."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which of these is NOT a primitive data type in Java?",
                        new String[]{"int", "boolean", "String", "double"},
                        2,
                        "String is a class in Java, not a primitive data type."
                ));


                currentQuizQuestions.add(new QuizQuestion(
                        "What is the purpose of the 'final' keyword in Java?",
                        new String[]{"To make a variable constant", "To end a program", "To finalize garbage collection", "To terminate a loop"},
                        0,
                        "The 'final' keyword makes a variable constant, preventing its value from being changed after initialization."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which collection type maintains insertion order in Java?",
                        new String[]{"HashSet", "TreeSet", "ArrayList", "HashMap"},
                        2,
                        "ArrayList maintains the order in which elements are inserted, unlike sets which don't guarantee order."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is method overloading in Java?",
                        new String[]{"Creating multiple methods with the same name but different parameters",
                                "Extending a method in a subclass",
                                "Creating private methods",
                                "Using the same method in different classes"},
                        0,
                        "Method overloading allows multiple methods with the same name but different parameter types or counts."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What does the 'extends' keyword do in Java?",
                        new String[]{"Creates a new class", "Implements an interface", "Inherits from a superclass", "Extends the length of an array"},
                        2,
                        "The 'extends' keyword is used to inherit from a superclass, creating an 'is-a' relationship."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "Which statement is used to handle exceptions in Java?",
                        new String[]{"try-catch", "if-else", "for-each", "switch-case"},
                        0,
                        "The try-catch block is used to handle exceptions that might occur during program execution."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is the difference between '==' and '.equals()' in Java?",
                        new String[]{"They are identical", "== compares values while .equals() compares references",
                                "== compares references while .equals() compares values", "== is faster than .equals()"},
                        2,
                        "For objects, == compares if two references point to the same object, while .equals() compares the content of objects."
                ));
                currentQuizQuestions.add(new QuizQuestion(
                        "What is an interface in Java?",
                        new String[]{"A class with only static methods", "A collection of abstract methods",
                                "A graphical user interface", "A connection between two classes"},
                        1,
                        "An interface in Java is a collection of abstract methods that can be implemented by classes."
                ));
                break;
        }

        // Shuffle questions for variety because everyone likes variety
        Collections.shuffle(currentQuizQuestions, new Random(System.currentTimeMillis()));

        // Replace the quiz panel with a new one

        //why did you do so much work outside of school bru
        //because ion do enugh in class

        tabbedPane.removeTabAt(tabbedPane.indexOfTab("Quiz"));
        tabbedPane.addTab("Quiz", createQuizPanel());

        /*Display the first question immediately (i dont know how the heck this even happened but for some reason
        THE FREAKING QUESTIONS WERENT SHOWING UP FOR THE FIRST PROMPT??? but we fixed it tho so we good now
         */
        if (!currentQuizQuestions.isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                JPanel quizPanel = (JPanel) tabbedPane.getComponentAt(tabbedPane.indexOfTab("Quiz"));

                // Find the question label
                JLabel questionLabel = null;
                JRadioButton[] optionButtons = new JRadioButton[4];
                JLabel feedbackLabel = null;

                // Find components in the quiz panel
                for (Component c : quizPanel.getComponents()) {
                    if (c instanceof JPanel) {
                        JPanel panel = (JPanel) c;
                        for (Component inner : panel.getComponents()) {
                            if (inner instanceof JLabel) {
                                questionLabel = (JLabel) inner;
                            }
                        }
                    } else if (c instanceof JScrollPane) {
                        JScrollPane scrollPane = (JScrollPane) c;
                        if (scrollPane.getViewport().getView() instanceof JPanel) {
                            JPanel optionsPanel = (JPanel) scrollPane.getViewport().getView();
                            int i = 0;
                            for (Component opt : optionsPanel.getComponents()) {
                                if (opt instanceof JRadioButton && i < 4) {
                                    optionButtons[i++] = (JRadioButton) opt;
                                }
                            }
                        }
                    } else if (c instanceof JLabel && c != questionLabel) {
                        feedbackLabel = (JLabel) c;
                    }
                }

                // Display the first question if we found the components
                if (questionLabel != null && optionButtons[0] != null) {
                    QuizQuestion question = currentQuizQuestions.get(0);
                    questionLabel.setText("Question 1: " + question.question);
                    for (int i = 0; i < 4; i++) {
                        optionButtons[i].setText(question.options[i]);
                        optionButtons[i].setVisible(true);
                    }
                    if (feedbackLabel != null) {
                        feedbackLabel.setText("");
                    }
                }
            });
        }
    }

    private void displayFirstQuestion() {
        // This method will be called after the UI is updated
        if (!currentQuizQuestions.isEmpty()) {
            // Get the quiz panel
            JPanel quizPanel = (JPanel) tabbedPane.getComponentAt(tabbedPane.indexOfTab("Quiz"));

            // Find the components needed to display the question
            JLabel questionLabel = null;
            JRadioButton[] optionButtons = new JRadioButton[4];
            JLabel feedbackLabel = null;

            // Find components (simplified search)
            for (Component c : quizPanel.getComponents()) {
                if (c instanceof JPanel && ((JPanel) c).getLayout() instanceof BorderLayout) {
                    JPanel panel = (JPanel) c;
                    for (Component inner : panel.getComponents()) {
                        if (inner instanceof JLabel) {
                            questionLabel = (JLabel) inner;
                        }
                    }
                }
            }

            // Display the first question if we found the components
            if (questionLabel != null) {
                // Display the first question
                currentQuestionIndex = 0;
                QuizQuestion question = currentQuizQuestions.get(0);
                questionLabel.setText("Question 1: " + question.question);
            }
        }
    }

    private void displayQuestion(int index, JLabel questionLabel, JRadioButton[] optionButtons, JLabel feedbackLabel) {
        if (index < currentQuizQuestions.size()) {
            QuizQuestion question = currentQuizQuestions.get(index);
            questionLabel.setText("Question " + (index + 1) + ": " + question.question);
            for (int i = 0; i < 4; i++) {
                optionButtons[i].setText(question.options[i]);
                optionButtons[i].setVisible(true);
            }
            feedbackLabel.setText("");
        }
    }

    private static class QuizQuestion {
        String question;
        String[] options;
        int correctAnswer;
        String explanation;

        public QuizQuestion(String question, String[] options, int correctAnswer, String explanation) {
            this.question = question;
            this.options = options;
            this.correctAnswer = correctAnswer;
            this.explanation = explanation;
        }
    }

    // Add this class for text wrapping in table cells
    private static class MultiLineTableCellRenderer extends JTextArea implements TableCellRenderer {
        public MultiLineTableCellRenderer() {
            setLineWrap(true);
            setWrapStyleWord(true);
            setOpaque(true);
        }

        public Component getTableCellRendererComponent(JTable table, Object value,
                                                       boolean isSelected, boolean hasFocus,
                                                       int row, int column) {
            if (isSelected) {
                setForeground(table.getSelectionForeground());
                setBackground(table.getSelectionBackground());
            } else {
                setForeground(table.getForeground());
                setBackground(table.getBackground());
            }
            setFont(table.getFont());
            setText((value == null) ? "" : value.toString());
            return this;
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> new RegentsUhUhUhTheTheUhhhhhh().setVisible(true));
    }
}
